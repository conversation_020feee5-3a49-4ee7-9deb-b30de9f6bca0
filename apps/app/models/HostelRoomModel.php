<?php

namespace Modules\App\Models;

use Phalcon\Mvc\Model\Behavior\Timestampable;
class HostelRoomModel extends DatatableManagerModel
{
	public function getSource()
	{
		return "hostel_room";
	}

	public function columnMap()
	{
		return array(
			'id'       		=>	'id',
			'hostel_id'		=>	'hostel_id',
			'title'    		=>	'title',
			'image'    		=>	'image',
			'imgs'    		=>	'imgs',
			'price'    		=>	'price',
			'maximum'  		=>	'maximum',
			'quantity' 		=>	'quantity',
			'area'     		=>	'area',
			'content'  		=>	'content',
			'gender'  		=>	'gender',
			'amenities'  	=>	'amenities',
			'url_review'	=>	'url_review',
			'creator_id' 	=>	'creator_id',
			'editor_id' 	=>	'editor_id',
			'created' 		=>	'created',
			'updated' 		=>	'updated',
			'status'   		=>	'status',
			'position' 		=>	'position',
		);
	}

	public function initialize()
	{
		$this->belongsTo('hostel_id', '\Modules\App\Models\HostelModel', 'id', ['alias'	=>	'hostel']);
		$this->belongsTo('creator_id', '\Modules\App\Models\ErpMemberModel', 'id', ['alias' => 'member', 'reusable' => true]);
		$this->hasMany('id', '\Modules\App\Models\HostRoomModel', 'hostel_room_id', ['alias' => 'hostRooms']);
		$this->hasManyToMany(
			'id', 'Modules\App\Models\HostHostelRoomServiceModel', 'hostel_room_id', 'service_id', 'Modules\App\Models\HostServiceModel', 'id', ['alias' => 'services']
		);
	}

	public function createHostelRoom($data)
	{
		// 1. Kiểm tra dữ liệu đầu vào
		$validation = $this->validateHostelRoomData($data);
		if (!$validation['success']) {
			return $validation;
		}

		// 2. Set default value
		$defaults = [
			'hostel_id' => null,
			'title' 		=> null,
			'price'     => '',
			'maximum'   => '',
			'quantity'  => '',
			'area' 		  => '',
			'content' 	=> '',
			'gender' 		=> '',
			'created'		=>	date('Y-m-d H:i:s'),
			'status'   	=> 1
		];

		// 3. Gộp dữ liệu
		$hostelRoomData = array_merge($defaults, $data);

		// 4. Gán dữ liệu cho model
		$this->assign($hostelRoomData);
		$this->content = trim($hostelRoomData['content']);
		$this->updateAmenities($hostelRoomData['amenities'] ?? []);
		$this->updateImage($data['files'] ?? [], $data['renameFiles'] ?? [], $data['originFiles'] ?? [], $data['newFiles'] ?? []);

		// 5. Lưu bản ghi
		if ($this->save()) {
			// 6. (Có thể mở rộng: tạo HostelInfo, log lịch sử, gửi thông báo...)
			// Tạm thời có thể chưa cần thiết
			return [
				'success' 	 => true,
				'message' 	 => 'Tạo mới loại phòng thành công',
				'id'      	 => $this->id,
				'hostelRoom' => $this // Trả về object model để xử lý tiếp
			];
		}

		return [
			'success' => false,
			'message' => 'Không thể tạo mới loại phòng',
			'errors'  => $this->getMessages()
		];
	}
	
	public function validateHostelRoomData($data)
	{
		$errors = [];

		$hostel = HostelModel::findFirstById($data['hostel_id'] ?? null);
		if (!$hostel) {
			$errors['hostel'] = 'Trọ không tồn tại';
		}

		if (empty($data['title'])) {
			$errors['title'] = 'Tên loại phòng không được để trống';
		} 

		if (empty($data['price'])) {
			$errors['price'] = 'Giá thuê không hợp lệ';
		} 

		if (empty($data['area']) || $data['area'] < 0 || $data['area'] > 10000) {
			$errors['area'] = 'Diện tích không hợp lệ';
		} 

		if (empty($data['quantity']) || $data['quantity'] < 0 || $data['quantity'] > 127) {
			$errors['quantity'] = 'Số phòng trống không hợp lệ';
		}

		if (empty($data['maximum']) || $data['maximum'] < 0 || $data['maximum'] > 127) {
			$errors['maximum'] = 'Số người tối đa không hợp lệ';
		}

		if ($data['gender'] == '') {
			$errors['gender'] = 'Đối tượng không được để trống';
		}

		if (!isset($data['amenities']) || empty($data['amenities'])) {
			$errors['amenities'] = 'Vui lòng chọn tiện nghi';
		}

		if (empty($data['content'])) {
			$errors['content'] = 'Mô tả không được để trống';
		}

		if (empty($data['files'] ?? []) && empty($data['newFiles'] ?? [])) {
			$errors['files'] = 'Hình ảnh không được để trống';
		}
		
		if (!empty($errors)) {
			return [
				'success' => false,
				'message' => 'Dữ liệu không hợp lệ',
				'errors'  => $errors
				// Error: Có thể dùng key => value để hiển thị lỗi
			];
		}

		return ['success' => true];
	}

	public function updateImage($files, $renameFiles, $originFiles, $newFile)
	{
		$targetDir = ROOT_PATH . '/public/uploads/guest/';
		$arrImgs = [];

		$deletedFiles = array_diff($originFiles, $files);
		foreach ($deletedFiles as $deletedFile) {
			$filePath = $targetDir . basename($deletedFile);
			if (file_exists($filePath)) unlink($filePath);
		}

		$arrImgs = [];
		$uploadedFiles = $files;
		if (!empty($originFiles)) {
			$arrImgs = $files;
			$uploadedFiles = $newFile;
		}

		foreach ($uploadedFiles as $key => $file) {
			$name = $renameFiles[$key];
			$targetFile = $targetDir . $name;
			$file->moveTo($targetFile);
			$arrImgs[] = '/uploads/guest/' . $name;
		}

		$imgs = [];
		foreach ($arrImgs as $keys => $img) {
			if (!empty($img)) {
				$objImg = new \stdClass();
				$objImg->src = $img;
				$objImg->default = ($keys == 0) ? 1 : 0;
				$imgs[] = $objImg;
			}
		}

		$this->imgs = json_encode($imgs);
		$this->image = $imgs[0]->src;
	}

	public function updateAmenities($amenities)
	{
		$amenities = array_fill_keys($amenities, "1");
		$amenitiesJson = json_encode($amenities);
		$this->amenities = $amenitiesJson;
	}

	/**
	 * Cập nhật thông tin nhà trọ
	 * 
	 * @param int $id ID nhà trọ cần cập nhật
	 * @param array $data Dữ liệu cập nhật
	 * @return array Kết quả cập nhật
	 */
	public function updateHostelRoom($id, $data)
	{
		// Tìm bản ghi cần cập nhật
		$hostelRoom = self::findFirstById($id);
		if (!$hostelRoom) {
			return [
				'success' => false,
				'message' => 'Không tìm thấy nhà trọ',
				'errors'  => ['id' => 'ID không tồn tại']
			];
		}

		$validation = $this->validateHostelRoomData($data);
		if (!$validation['success']) {
			return $validation;
		}

		// Gán dữ liệu mới
		$hostelRoom->assign($data);
		$hostelRoom->content = trim($data['content']);
		$hostelRoom->updateAmenities($data['amenities'] ?? []);
		$hostelRoom->updateImage($data['files'] ?? [], $data['renameFiles'] ?? [], $data['originFiles'] ?? [], $data['newFiles'] ?? []);
		$hostelRoom->updated = date('Y-m-d H:i:s');

		// Cập nhật người sửa
		if (!empty($data['editor_id'])) {
			$hostelRoom->editor_id = $data['editor_id'];
		}

		// Lưu lại
		if ($hostelRoom->save()) {
			return [
				'success' => true,
				'message' => 'Cập nhật loại phòng thành công',
				'hostelRoom'  => $hostelRoom

				//Trả về object model để xử lý tiếp
			];
		}

		return [
			'success' => false,
			'message' => 'Không thể cập nhật loại phòng',
			'errors'  => $hostelRoom->getMessages()
		];
	}

	public function DTOClientDetail()
	{
		return [
			'id' 						=> $this->id,
			'title' 				=> $this->title,
			'price' 				=> $this->price,
			'area' 					=> $this->area,
			'quantity' 			=> $this->quantity,
			'maximum' 			=> $this->maximum,
			'gender' 				=> $this->gender,
			'amenities' 		=> $this->getProps($this->amenities),
			'content' 			=> $this->content,
			'image' 				=> $this->image,
			'imgs' 					=> $this->getImgs(),
			'url_review' 		=> $this->url_review,
		];
	}

	private function getProps($props)
	{
		$props = json_decode($props);
		$result = [];
		foreach ($props as $key => $value) {
			$result[] = [
				'key' 		=> $key,
				'value' 	=> AdHostelPropertiesModel::findFirstByCode($key)->title,
			];
		}
		return $result;
	}

	private function getImgs()
	{
		$imgs = json_decode($this->imgs);
		$result = [];
		foreach ($imgs as $img) {
			$result[] = $img->src;
		}
		return $result;
	}
}
