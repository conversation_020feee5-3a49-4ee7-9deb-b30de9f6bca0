<?php

namespace Modules\App\Models;

use Phalcon\Mvc\Model\Behavior\Timestampable;

class AdHostelPremiumModel extends DatatableManagerModel
{
	public function columnMap()
	{
		return array(
			'id' 							=> 'id',
			'ad_hostel_id' 		=> 'ad_hostel_id',
			'premium_type' 		=> 'premium_type',
			'register_day' 		=> 'register_day',
			'expired' 				=> 'expired',
			'created'					=> 'created',
			'updated'					=> 'updated'
		);
	}

	public function getSource()
	{
		return 'ad_hostel_premium';
	}

	public function initialize()
	{
		$this->hasOne('ad_hostel_id', '\Modules\App\Models\AdHostelModel', 'id', array(
			'alias' 		=> 'ad_hostel',
			'reusable' 	=> true
		));

		$this->addBehavior(
      new Timestampable([
        'beforeCreate' => [
          'field' => 'created',
          'format' => 'Y-m-d H:i:s'
        ],
        'beforeUpdate' => [
          'field' => 'updated',
          'format' => 'Y-m-d H:i:s'
        ]
      ])
    );
	}

	public function storePremium($hostel, $days, $type)
	{
		try {
			$hostelPremium = !empty($hostel->premium) ? $hostel->premium : new self();
			$exp = $hostelPremium->expired > date('Y-m-d H:i:s') ? $hostelPremium->expired : date('Y-m-d H:i:s');
			$hostelPremium->assign([
				'ad_hostel_id' 	=> $hostel->id,
				'premium_type' 	=> $type,
				'register_day' 	=> date('Y-m-d H:i:s'),
				'expired' 			=> date('Y-m-d H:i:s', strtotime($exp . ' + ' . $days . ' days')),
			]);
			
			if ($hostelPremium->save()) {	
				return [
					'success' => true,
					'data' 		=> $hostelPremium
				];
			}

			return [
				'success' => false,
				'message' => 'Lỗi khi lưu lịch sử nâng cấp HOT.'
			];

		} catch (\Exception $e) {
			return [
				'success' => false,
				'message' => 'Lỗi khi lưu lịch sử nâng cấp HOT.'
			];
		}
	}

	public function isExpired()
	{
		return $this->expired < date('Y-m-d H:i:s');
	}

	public function getNewExpiredDate($days)
	{
		if ($this->isExpired()) {
			return date('Y-m-d H:i:s', strtotime('+' . $days . ' days'));
		}
		return date('Y-m-d H:i:s', strtotime('+' . $days . ' days', strtotime($this->expired)));
	}

	public function createOrUpdate($hostel, $package)
	{
		try {
			$isNew = empty($this->id);
			
			if ($isNew) {
				$this->assign([
					'ad_hostel_id' => $hostel->id,
					'premium_type' => $package->type,
					'register_day' => date('Y-m-d H:i:s'),
					'expired' 		 => date('Y-m-d', strtotime("+{$package->day} day")),
				]);
			} else {
				$this->premium_type = $package->type;
				$this->expired = $this->getNewExpiredDate($package->day);
			}

			if (!$this->save()) {
				throw new \Exception($this->getMessages()[0]);
			}

			return [
				'success' => true,
				'data' => $this,
				'is_new' => $isNew
			];

		} catch (\Exception $e) {
			return [
				'success' => false,
				'message' => $e->getMessage()
			];
		}
	}

	/**
	 * Hủy premium
	 * @return bool
	 * @throws \Exception
	 */
	public function cancel()
	{
		$this->expired = date('Y-m-d H:i:s');
		$this->updated = date('Y-m-d H:i:s');
		
		if (!$this->save()) {
			throw new \Exception($this->getMessages()[0]);
		}
		
		return true;
	}

	/**
	 * Kiểm tra xem có phải là premium HOT không
	 * @return bool
	 */
	public function isHot()
	{
		return $this->premium_type === 'hot';
	}

	/**
	 * Lấy thời gian còn lại của premium
	 * @return int Số giây còn lại
	 */
	public function getRemainingTime()
	{
		if ($this->isExpired()) {
			return 0;
		}
		return strtotime($this->expired) - time();
	}

	/**
	 * Lấy số ngày còn lại của premium
	 * @return int Số ngày còn lại
	 */
	public function getRemainingDays()
	{
		return ceil($this->getRemainingTime() / 86400); // 86400 = 24 * 60 * 60
	}
	
}