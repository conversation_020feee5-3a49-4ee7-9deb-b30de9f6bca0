<?php

namespace Modules\App\Models;

use Phalcon\Mvc\Model;
use Phalcon\Mvc\Model\Query;
use Phalcon\Mvc\Model\Resultset\Simple as Resultset;
use Phalcon\Mvc\Model\Manager as ModelsManager;
use Phalcon\Mvc\Model\Behavior\Timestampable;
use Modules\Library\Oh\User;

class HostelModel extends DatatableManagerModel
{
	public function getSource()
	{
		return "hostel";
	}

	public function columnMap()
	{
		return array(
			'id'                =>  'id',
			'host_id'           =>  'host_id',
			'type_id'           =>  'type_id',
			'name'              =>  'name',
			'code'              =>  'code',
			'image'             =>  'image',
			'room'              =>  'room',
			'area'          		=>  'area',
			'address'           =>  'address',
			'street'            =>  'street',
			'ward_code'         =>  'ward_code',
			'district_code'     =>  'district_code',
			'province_code'     =>  'province_code',
			'full_address'      =>  'full_address',
			'longitude'         =>  'longitude',
			'latitude'          =>  'latitude',
			'status'            =>  'status',
			'created'           =>  'created',
			'updated'           =>  'updated',
			'creator_id'        =>  'creator_id',
			'editor_id'         =>  'editor_id'
		);
	}

	public function initialize()
	{
		$this->belongsTo('id', '\Modules\App\Models\AdHostelModel', 'hostel_id', ['alias' =>	'adHostel']);
		$this->belongsTo('host_id', '\Modules\App\Models\HostModel', 'id', ['alias' =>	'host']);
		$this->belongsTo('type_id', '\Modules\App\Models\HostelTypeModel', 'id', ['alias'	=>	'hostel_type']);
		$this->belongsTo('ward_code', '\Modules\App\Models\LocationWardModel', 'code', ['alias'	=>	'ward']);
		$this->belongsTo('district_code', '\Modules\App\Models\LocationDistrictModel', 'code', ['alias'	=>	'district']);
		$this->belongsTo('province_code', '\Modules\App\Models\LocationProvinceModel', 'code', ['alias'	=>	'province']);
		$this->belongsTo('creator_id', '\Modules\App\Models\ErpMemberModel', 'id', ['alias' => 'creator', 'reusable' => true]);
		$this->hasOne('id', '\Modules\App\Models\HostelInfoModel', 'hostel_id', ['alias' => 'info', 'reusable' => true]);
		$this->hasMany('id', '\Modules\App\Models\HostelRoomModel', 'hostel_id', ['alias' => 'rooms']);
		$this->hasMany('id', '\Modules\App\Models\HostTenantModel', 'hostel_id', ['alias' => 'tenants', 'reusable'  => true]);
		$this->hasMany('id', '\Modules\App\Models\HostServiceModel', 'hostel_id', ['alias' => 'services', 'reusable'  => true]);
		$this->hasMany('id', '\Modules\App\Models\HostContractModel', 'hostel_id', ['alias' => 'contracts', 'reusable'  => true]);
		$this->hasMany('id', '\Modules\App\Models\HostTransactionModel', 'hostel_id', ['alias' => 'transactions', 'reusable'  => true]);
		$this->hasMany('id', '\Modules\App\Models\LocationDistanceModel', 'hostel_id', ['alias' => 'distances', 'reusable'  => true]);
		$this->addBehavior(new Timestampable([
			'beforeCreate' => [
				'field' => 'created',
				'format' => 'Y-m-d H:i:s',
			],
			'beforeUpdate' => [
				'field' => 'updated',
				'format' => 'Y-m-d H:i:s',
			],
		]));
	}

	public function GetHostelItem($params = null, $option = null)
	{
		$builder = $this->modelsManager->createBuilder()
			->columns(['H.*'])
			->from(['H' => 'Modules\App\Models\HostelModel'])
			->leftJoin('Modules\App\Models\HostModel', 'HS.id = H.host_id', 'HS')
			->leftJoin('Modules\App\Models\AdHostelModel', 'AH.hostel_id = H.id', 'AH')
			->leftJoin('Modules\App\Models\UserModel', 'U.id = HS.user_id', 'U')
			->leftJoin('Modules\App\Models\HostelRoomModel', 'R.hostel_id = H.id', 'R');

		if (!empty($params['where'])) {
			$builder->where($params['where']);
		}

		if (!empty($params['order'])) {
			$builder->orderBy($params['order']);
		} else {
			$builder->orderBy("H.created DESC");
		}

		if (!empty($params['limit'])) {
			$builder->limit($params['limit']);
		}

		if (!empty($params['offset'])) {
			$builder->offset($params['offset']);
		}

		$builder->groupBy("H.id");

		$params['bind'] = !empty($params['bind']) ? $params['bind'] : null;

		$result = $builder->getQuery()->execute($params['bind']);
		return $result;
	}

	private function getController()
	{
		return "hostel";
	}

	public function my_url($_format, $id = null)
	{
		$Ohi = new \Modules\Library\Oh\Ohi();
		return $Ohi->baseAdminUrl($this->getController() . "/index/{$_format}/{$id}");
	}

	public function datatable_column()
	{
		$hostModel = new HostModel();
		$hostelTypeModel = new HostelTypeModel();
		$locationProvinceModel = new LocationProvinceModel();

		$init_data = array(
			array(
				'name'      => 'h@name',
				'label'     => 'Tên nhà trọ',
				'filter'    => array(
					'type'  => 'text'
				)
			),
			array(
				'name'      => 'h@code',
				'label'     => 'Mã nhà trọ',
				'filter'    => array(
					'type'  => 'text'
				)
			),
			array(
				'name'      => 'h@host_id',
				'label'     => 'Chủ trọ',
				'filter'    => array(
					'type'  => 'select',
					'value' => []
				)
			),
			array(
				'name'      => 'h@type_id',
				'label'     => 'Loại nhà trọ',
				'filter'    => array(
					'type'  => 'select',
					'value' => []
				)
			),
			array(
				'name'      => 'h@province_code',
				'label'     => 'Tỉnh/Thành phố',
				'filter'    => array(
					'type'  => 'select',
					'value' => $locationProvinceModel->GetListItem('code', 'name')
				)
			),
			array(
				'name'      => 'h@status',
				'width'     => '80px',
				'label'     => 'Trạng thái',
				'filter'    => array(
					'type'  => 'select',
					'value' => array(0 => 'Không hoạt động', 1 => 'Hoạt động')
				)
			),
			array(
				'name'      => 'm@username',
				'label'     => 'Người tạo',
				'width'     => '80px',
				'filter'    => array(
					'type'  => 'text'
				)
			),
			array(
				'name'      => 'h@created',
				'width'     => '100px',
				'label'     => 'Ngày tạo',
				'filter'    => array(
					'type'  => 'daterange'
				)
			),
			array(
				'name'      => 'h@id',
				'label'     => '<a href="' . $this->my_url('add') . '" class="btn btn-primary"><i class="fa fa-plus"></i> Thêm</a>',
				'width'     => '100px'
			)
		);
		return $init_data;
	}

	public function datatable_json_data()
	{
		$result = $this->datatable_find([
			'select'        => ['h.name', 'h.code', 'h.status', 'h.full_address', 'hs.name AS host_name', 'ht.title AS hostel_type', 'lp.name AS province_name', 'm.username', 'h.created', 'h.id'],
			'from'          => ['h' => 'Modules\App\Models\HostelModel'],
			'leftJoin'      => [
				'Modules\App\Models\HostModel' => ['alias' => 'hs', 'on' => 'h.host_id=hs.id'],
				'Modules\App\Models\HostelTypeModel' => ['alias' => 'ht', 'on' => 'h.type_id=ht.id'],
				'Modules\App\Models\LocationProvinceModel' => ['alias' => 'lp', 'on' => 'h.province_code=lp.code'],
				'Modules\App\Models\ErpMemberModel' => ['alias' => 'm', 'on' => 'h.creator_id=m.id'],
			],
			'group_by'      => array('h.id')
		]);

		return $result;
	}

	/**
	 * Tạo mới một bản ghi hostel
	 * 
	 * @param array $data Dữ liệu nhà trọ cần tạo
	 * @return array Kết quả tạo bản ghi (thành công/thất bại, thông tin lỗi nếu có)
	 */
	public function createHostel($data, $user)
	{
		// 1. Kiểm tra dữ liệu đầu vào
		$validation = $this->validateHostelData($data);
		if (!$validation['success']) {
			return $validation;
		}

		// 2. Set default value
		$defaults = [
			'host_id'				=> null,
			'type_id'				=> null,
			'room'          => 0,
			'area'          => 0,
			'address'       => '',
			'street'        => '',
			'ward_code'     => '',
			'district_code' => '',
			'status'        => 1
		];

		// 3. Gộp dữ liệu
		$hostelData = array_merge($defaults, $data);

		// 4. Gán dữ liệu cho model
		$this->assign($hostelData);

		$result = $this->handleHostCreateAndVerify($data, $user);
		if (!$result['success'] || !empty($result['requestVerify'])) {
			return $result;
		}
		
		$this->host_id = $user->host->id;
		$this->code = $this->createSlug('name');
		$this->updateLocationInfo($hostelData);
		$this->updateImage($data['files'] ?? [], $data['renameFiles'] ?? [], $data['originFiles'] ?? [], $data['newFiles'] ?? []);

		// 5. Lưu bản ghi
		if ($this->save()) {
			// 6. (Có thể mở rộng: tạo HostelInfo, log lịch sử, gửi thông báo...)
			// Tạm thời có thể chưa cần thiết
			return [
				'success' => true,
				'message' => 'Tạo mới nhà trọ thành công',
				'id'      => $this->id,
				'hostel'  => $this // Trả về object model để xử lý tiếp
			];
		}

		return [
			'success' => false,
			'message' => 'Không thể tạo mới nhà trọ',
			'errors'  => $this->getMessages()
		];
	}
	
	public function validateHostelData($data)
	{
		$errors = [];

		$hostelType = HostelTypeModel::findFirstById($data['type_id'] ?? null);
		if (!$hostelType) {
			$errors['type_id'] = 'Loại nhà trọ không tồn tại';
		}

		if (empty($data['name'])) {
			$errors['name'] = 'Tên nhà trọ không được để trống';
		} 

		if (empty($data['room']) || $data['room'] < 0 || $data['room'] > 127) {
			$errors['room'] = 'Tổng số phòng không hợp lệ';
		} 

		if (empty($data['area']) || $data['area'] < 0 || $data['area'] > 10000) {
			$errors['area'] = 'Diện tích không không hợp lệ';
		} 

		$province = LocationProvinceModel::findFirstByCode($data['province_code'] ?? null);
		if (!$province) {
			$errors['province_code'] = 'Tỉnh/Thành phố không tồn tại';
		}

		$district = LocationDistrictModel::findFirstByCode($data['district_code'] ?? null);
		if (!$district) {
			$errors['district_code'] = 'Quận/Huyện không tồn tại';
		}

		$ward = LocationWardModel::findFirstByCode($data['ward_code'] ?? null);
		if (!$ward) {
			$errors['ward_code'] = 'Phường/Xã không tồn tại';
		}
		
		$street = LocationStreetModel::findFirstById($data['street'] ?? null);
		if (!$street) {
			$errors['street'] = 'Đường không tồn tại';
		}

		if (empty($data['address'])) {
			$errors['address'] = 'Địa chỉ không được để trống';
		}

		if (empty($data['files'] ?? []) && empty($data['newFiles'] ?? [])) {
			$errors['files'] = 'Hình ảnh không được để trống';
		}

		if (!empty($errors)) {
			return [
				'success' => false,
				'message' => 'Dữ liệu không hợp lệ',
				'errors'  => $errors
				// Error: Có thể dùng key => value để hiển thị lỗi
			];
		}

		return ['success' => true];
	}

	public function updateLocationInfo($data)
	{
		$item_street = null;
		if (!empty($data['street'])) {
			$item_street = \Modules\App\Models\LocationStreetModel::findFirstById($data['street']);
		}

		$item_province = null;
		if (!empty($data['province_code'])) {
			$item_province = \Modules\App\Models\LocationProvinceModel::findFirstByCode($data['province_code']);
		}

		$map = \Modules\Library\Oh\Maps::getLogLat(
			$data['address'] ?? '',
			(!empty($item_street) ? $item_street->name : ''),
			(!empty($item_province) ? $item_province->name : '') . ', Việt Nam'
		);

		if (!empty($map)) {
			$this->longitude = $map['longitude'];
			$this->latitude 		= $map['latitude'];
			$this->full_address = $map['full_address'];

			return true;
		}

		return false;
	}

	public function updateImage($files, $renameFiles, $originFiles, $newFile)
	{
		$targetDir = ROOT_PATH . '/public/uploads/guest/';
		$arrImgs = [];

		$deletedFiles = array_diff($originFiles, $files);
		foreach ($deletedFiles as $deletedFile) {
			$filePath = $targetDir . basename($deletedFile);
			if (file_exists($filePath)) unlink($filePath);
		}

		$arrImgs = [];
		$uploadedFiles = $files;
		if (!empty($originFiles)) {
			$arrImgs = $files;
			$uploadedFiles = $newFile;
		}

		foreach ($uploadedFiles as $key => $file) {
			$name = $renameFiles[$key];
			$targetFile = $targetDir . $name;
			$file->moveTo($targetFile);
			$arrImgs[] = '/uploads/guest/' . $name;
		}

		$this->image = $arrImgs[0];
	}

	public function handleHostCreateAndVerify($data, $user) 
	{
		if (!$user->host) {
			$reusltHost = HostModel::init()->createHost($data['host'], $user);
			if (!$reusltHost['success']) {
				return _handleResult($reusltHost);
			}
		}

		$resultHostVerify = HostVerifyModel::init()->handleHostVerify($user->host);
		if (!empty($resultHostVerify['success']) && !empty($resultHostVerify['errors'])) {
			return _handleResult($resultHostVerify);
		}

		if ($resultHostVerify['success']) {
			return [
				'requestVerify' => true,
				'host' 					=> [
					'id' 		=> $resultHostVerify['hostVerify']->host_id,
					'phone' => $resultHostVerify['hostVerify']->phone
				]
			];
		};

		function _handleResult($result) {
			$errors = [];
			foreach ($result['errors'] as $field => $msg) {
				$errors["host[$field]"] = $msg;
			}
			$result['errors'] = $errors;
			return $result;
		}

		return ['success' => true];
	}

	/**
	 * Cập nhật thông tin nhà trọ
	 * 
	 * @param int $id ID nhà trọ cần cập nhật
	 * @param array $data Dữ liệu cập nhật
	 * @return array Kết quả cập nhật
	 */
	public function updateHostel($id, $data)
	{
		// Tìm bản ghi cần cập nhật
		$hostel = self::findFirstById($id);
		if (!$hostel) {
			return [
				'success' => false,
				'message' => 'Không tìm thấy nhà trọ',
				'errors'  => ['id' => 'ID không tồn tại']
			];
		}

		$validation = $this->validateHostelData($data);
		if (!$validation['success']) {
			return $validation;
		}

		// Gán dữ liệu mới
		$hostel->assign($data);
		$hostel->updateLocationInfo($data);
		$hostel->updateImage($data['files'] ?? [], $data['renameFiles'] ?? [], $data['originFiles'] ?? [], $data['newFiles'] ?? []);
		$hostel->updated = date('Y-m-d H:i:s');

		// Cập nhật người sửa
		if (!empty($data['editor_id'])) {
			$hostel->editor_id = $data['editor_id'];
		}

		// Lưu lại
		if ($hostel->save()) {
			return [
				'success' => true,
				'message' => 'Cập nhật nhà trọ thành công',
				'id'  		=> $hostel->id,
				'hostel'  => $hostel

				//Trả về object model để xử lý tiếp
			];
		}

		return [
			'success' => false,
			'message' => 'Không thể cập nhật nhà trọ',
			'errors'  => $hostel->getMessages()
		];
	}
}
