<?php

namespace Modules\App\Models;

use Phalcon\Mvc\Model\Behavior\Timestampable;

class HostTenantModel extends DatatableManagerModel
{
    public function getSource()
    {
        return "host_tenant";
    }

    public function columnMap()
    {
        return [
            'id'                 => 'id',
            'hostel_id'          => 'hostel_id',
            'user_id'            => 'user_id',
            'fullname'           => 'fullname',
            'birthday'           => 'birthday',
            'gender'             => 'gender',
            'phone'              => 'phone',
            'email'              => 'email',
            'job'                => 'job',
            'national_id'        => 'national_id',
            'national_id_date'   => 'national_id_date',
            'national_id_place'  => 'national_id_place',
            'national_id_image'  => 'national_id_image',
            'address'            => 'address',
            'ward_code'          => 'ward_code',
            'district_code'      => 'district_code',
            'province_code'      => 'province_code',
            'street'             => 'street',
            'is_temp_resident'   => 'is_temp_resident',
            'status'             => 'status',
            'creator_id'         => 'creator_id',
            'editor_id'          => 'editor_id',
            'created'            => 'created',
            'updated'            => 'updated'
        ];
    }

    public function initialize()
    {
        $this->belongsTo('hostel_id', '\Modules\App\Models\HostelModel', 'id', [
            'alias' => 'hostel',
            'reusable' => true
        ]);

        $this->belongsTo('creator_id', '\Modules\App\Models\ErpMemberModel', 'id', [
            'alias' => 'creator',
            'reusable' => true
        ]);

        $this->belongsTo('editor_id', '\Modules\App\Models\ErpMemberModel', 'id', [
            'alias' => 'editor',
            'reusable' => true
        ]);

        $this->addBehavior(new Timestampable([
            'beforeCreate' => [
                'field' => 'created',
                'format' => 'Y-m-d H:i:s'
            ],
            'beforeUpdate' => [
                'field' => 'updated',
                'format' => 'Y-m-d H:i:s'
            ]
        ]));
    }
}
