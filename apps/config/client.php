<?php

// API tìm kiếm trọ
$router->add('/api/client/ad-hostel/list', [
  'namespace'  => 'Modules\Frontend\Controllers\Api\Client',
  'controller' => 'ad-hostel-api',
  'action'     => 'list',
])->via(['GET']);

// API lấy chi tiết tin đăng trọ
$router->add('/api/client/ad-hostel/{id:[0-9]+}', [
  'namespace'  => 'Modules\Frontend\Controllers\Api\Client',
  'controller' => 'ad-hostel-api',
  'action'     => 'detail',
])->via(['GET']);

// API lưu tin trọ yêu thích
$router->add('/api/client/ad-hostel/{id:[0-9]+}/save', [
  'namespace'  => 'Modules\Frontend\Controllers\Api\Client',
  'controller' => 'ad-hostel-api',
  'action'     => 'saveHostel',
])->via(['POST']);

// API lấy danh sách trọ đã lưu
$router->add('/api/client/ad-hostel/saved', [
  'namespace'  => 'Modules\Frontend\Controllers\Api\Client',
  'controller' => 'ad-hostel-api',
  'action'     => 'getSaved',
])->via(['GET']);

//Home
$router->add('/api/client/home/<USER>', [
  'namespace'  => 'Modules\Frontend\Controllers\Api\Client',
  'controller' => 'home-api',
  'action'     => 'index',
])->via(['GET']);
