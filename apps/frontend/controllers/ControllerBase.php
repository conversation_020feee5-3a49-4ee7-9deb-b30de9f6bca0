<?php

namespace Modules\Frontend\Controllers;

use Modules\App\Models\HostelTypeModel;
use Modules\App\Models\NotificationTypeModel;
use Modules\App\Models\UserModel;
use Modules\Library\Casso\Casso;
use Modules\Library\Oh\JWT;
use Phalcon\Mvc\Controller;

class ControllerBase extends Controller
{

	protected $trust_ip 	= FALSE;
	protected $trust_uri	= "";

	protected function onConstruct()
	{
		$this->view->assets 			= $this->url->get() . '/frontend/home';
		$this->view->frontend 		= $this->url->get() . '/frontend';
		$this->view->assets_dir		= '/frontend/home';
		$this->view->setTemplateAfter('template');
		if ($this->trust_ip == TRUE) $this->trust_ip($this->trust_uri);

		$this->view->listType = HostelTypeModel::find([
			'status'	=>	'status = :status:',
			'bind'		=>	['status'	=>	1],
			'order'		=>	'position ASC, created DESC'
		]);

		$this->view->listNotifsType = NotificationTypeModel::find([
			"conditions"	=>	"status = :status:",
			"bind"				=>	["status" =>	1],
			"order"				=>	"position ASC, created DESC"
		]);

		$this->checkAndProcessJwtToken();
	}

	protected function trust_ip($redirect_uri = NULL)
	{

		$ip 			= $this->get_ip();
		$ip_list 	= $this->list_ip();

		if ((empty($ip_list) || !in_array($ip, $ip_list)) && $this->dispatcher->getControllerName() != 'error') {

			if (!empty($redirect_uri)) {
				$this->response->redirect($redirect_uri);
			} else {
				$this->response->redirect('/401');
			}
		}
	}

	protected function get_ip()
	{

		$ip;

		if (getenv("HTTP_X_FORWARDED_FOR"))
			$ip = getenv("HTTP_X_FORWARDED_FOR");

		else if (getenv("HTTP_X_REAL_IP"))
			$ip = getenv("HTTP_X_REAL_IP");

		else if (getenv("HTTP_CLIENT_IP"))
			$ip = getenv("HTTP_CLIENT_IP");

		else if (getenv("HTTP_X_CLIENT_IP"))
			$ip = getenv("HTTP_X_CLIENT_IP");

		else if (getenv("HTTP_X_CLUSTER_CLIENT_IP"))
			$ip = getenv("HTTP_X_CLUSTER_CLIENT_IP");

		else if (getenv("REMOTE_ADDR"))
			$ip = getenv("REMOTE_ADDR");

		else
			$ip = "UNKNOWN";

		$ip = explode(',', $ip);
		return $ip[0];
	}

	protected function list_ip()
	{

		$ips 	= array();
		$path = realpath(ROOT_PATH . '/public/static/ip.txt');

		if ($path && is_file($path) && file_exists($path)) {

			$file = @file_get_contents($path);
			if ($file) {
				$file 	= preg_replace('/\[[^\]]+\]/', '', $file);
				$ips 	= preg_split('/[\n\s,]+/', $file);
			}
		}

		return $ips;
	}

	protected function checkAndProcessJwtToken()
	{
		// Kiểm tra xem có token trong cookie không
		if ($this->cookies->has('userToken') && !$this->session->has('user_info')) {
			try {
				// Lấy token từ cookie
				$token = $this->cookies->get('userToken')->getValue();

				// Giải mã token
				$payload = JWT::decode($token, $this->config->jwt->secret, ['HS256']);

				// Kiểm tra xem token có chứa ID người dùng không
				if (isset($payload->id)) {
					// Tìm thông tin người dùng từ database
					$user = UserModel::findFirst([
						'conditions' => 'id = :id:',
						'bind' => ['id' => $payload->id]
					]);

					// Nếu tìm thấy người dùng, lưu thông tin vào session
					if ($user) {
						$this->session->set('user_info', [
							'id'        => $user->id,
							'phone'     => !empty($user->phone) ? $user->phone : null,
							'fullname'  => $user->fullname,
							'email'     => !empty($user->email) ? $user->email : null,
							'is_verify' => $user->is_verify,
						]);
					}
				}
			} catch (\UnexpectedValueException $e) {
				// Token không hợp lệ hoặc đã hết hạn
				$this->cookies->delete('userToken');
				$this->session->remove('user_info');
				$this->logger->error("JWT token error: " . $e->getMessage());
			} catch (\DomainException $e) {
				// Lỗi giải mã token
				$this->cookies->delete('userToken');
				$this->session->remove('user_info');
				$this->logger->error("JWT token error: " . $e->getMessage());
			} catch (\Exception $e) {
				// Các lỗi khác
				$this->cookies->delete('userToken');
				$this->session->remove('user_info');
				$this->logger->error("JWT token error: " . $e->getMessage());
			}
		}
	}
}
