<?php

namespace Modules\Frontend\Controllers\Api\Client;

use Modules\Frontend\Controllers\Api\ApiControllerBase;
use Modules\App\Models\LocationProvinceModel;
use Modules\App\Models\AdHostelModel;

class HomeApiController extends ApiControllerBase
{
  public function indexAction($province_code = null)
  {
    try {
      if (empty($province_code)) {
        return $this->sendErrorResponse('Mã tỉnh/thành phố không hợp lệ', [], $this->statusCode::BAD_REQUEST);
      }

      // Kiểm tra tỉnh/thành phố tồn tại
      $province = LocationProvinceModel::findFirstByCode($province_code);
      if (!$province) {
        return $this->sendErrorResponse('Không tìm thấy tỉnh/thành phố', [], $this->statusCode::NOT_FOUND);
      }

      $baseConditions = "AH.is_published = :active: AND AH.locked = :locked: AND H.status = :active: AND H.province_code = :province_code:";
      $baseBind = [
        'province_code'     => $province_code,
        'active'            => 1,
        'locked'            => 0,
      ];

      // Lấy danh sách trọ nổi bật trong tỉnh/thành phố
      $featuredHostels = AdHostelModel::init()->GetItem([
        'where' => $baseConditions . " AND HP.id is not null AND HP.premium_type = :premium_type: AND HP.expired >= NOW()",
        'bind'  => array_merge($baseBind, ['premium_type' => 'hot']),
        'order' => "AH.published DESC",
        'limit' =>  20
      ]);

      // Lấy danh sách trọ có video review
      $videoReviewHostels = AdHostelModel::init()->GetItem([
        'where' => $baseConditions . " AND AH.url_review != '' AND AH.url_review is not null",
        'bind'  => $baseBind,
        'order' => "AH.published DESC",
        'limit' => 25
      ]);

      // Lấy danh sách trọ theo loại 2
      $hostelType2 = AdHostelModel::init()->GetItem([
        'where'	=>	'H.status = :status: and AH.is_published = :is_published: and AH.locked = :locked: and H.type_id = :type_id: AND H.province_code = :province_code:',
        'bind'	=>	['status' => 1, 'is_published' => 1, 'locked' => 0, 'type_id' => 2, 'province_code' => $province_code],
        'order'	=>	'AH.published DESC',
        'limit'	=>	20
      ]);

      // Lấy danh sách trọ theo loại 3
      $hostelType3 = AdHostelModel::init()->GetItem([
        'where'	=>	'H.status = :status: and AH.is_published = :is_published: and AH.locked = :locked: and H.type_id = :type_id: AND H.province_code = :province_code:',
        'bind'	=>	['status' => 1, 'is_published' => 1, 'locked' => 0, 'type_id' => 3, 'province_code' => $province_code],
        'order'	=>	'AH.published DESC',
        'limit'	=>	20
      ]);

      // Chuẩn bị dữ liệu trả về
      $data = [
        'province' => [
          'id'          => $province->id,
          'code'        => $province->code,
          'name'        => $province->name
        ],
        'hot_hostels'           => $this->formatHostelList($featuredHostels),
        'video_review_hostels'  => $this->formatHostelList($videoReviewHostels),
        'hostel_type2'          => $this->formatHostelList($hostelType2),
        'hostel_type3'          => $this->formatHostelList($hostelType3)
      ];
      
      return $this->sendSuccessResponse('Dữ liệu trang chủ theo tỉnh/thành phố', $data);
    } catch (\Exception $e) {
      $this->logger->error('HomeApi Error: ' . $e->getMessage());
      return $this->sendErrorResponse('Có lỗi xảy ra khi lấy dữ liệu', [], $this->statusCode::INTERNAL_SERVER_ERROR);
    }
  }

  private function formatHostelList($hostels)
  {
    $result = [];
    foreach ($hostels as $hostel) {
      $result[] = $hostel->DTOClientListItem();
    }
    return $result;
  }
}
