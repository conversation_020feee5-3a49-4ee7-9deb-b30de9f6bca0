<?php

namespace Modules\Frontend\Controllers\Api\Client;

use Modules\App\Models\HostContractModel;
use Modules\App\Models\HostContractTenantModel;
use Modules\App\Models\HostRoomModel;
use Modules\App\Models\HostTenantModel;
use Modules\App\Models\UserModel;
use Modules\Frontend\Controllers\Api\ApiControllerBase;

class ClientRentalApiController extends ApiControllerBase
{
  public function onConstruct()
  {
    parent::onConstruct();
  }

  /**
   * API lấy danh sách phòng mà user đang thuê/đã từng thuê
   * GET /api/client/rental/rooms
   */
  public function getRoomsAction()
  {
    try {
      $authHeader = $this->request->getHeader('Authorization');
      $payload = $this->getPayload($authHeader);

      if (empty($payload) || empty($payload->userId)) {
        return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
      }

      $user = UserModel::findFirstById($payload->userId);
      if (!$user) {
        return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_REQUEST);
      }

      // Tìm tenant dựa trên user_id
      $tenants = HostTenantModel::find([
        'conditions' => 'user_id = :user_id: AND status = 1',
        'bind' => [
          'user_id' => $user->id
        ]
      ]);

      if ($tenants->count() == 0) {
        return $this->sendSuccessResponse('Danh sách phòng thuê', [
          'items' => [],
          'pagination' => [
            'page'      => 1,
            'per_page'  => 0,
            'total'     => 0
          ]
        ]);
      }

      // Lấy tất cả contract_tenant của user
      $tenantIds = [];
      foreach ($tenants as $tenant) {
        $tenantIds[] = $tenant->id;
      }

      $contractTenants = HostContractTenantModel::find([
        'conditions' => 'tenant_id IN ({tenantIds:array})',
        'bind' => ['tenantIds' => $tenantIds]
      ]);

      if ($contractTenants->count() == 0) {
        return $this->sendSuccessResponse('Danh sách phòng thuê', [
          'items' => [],
          'pagination' => [
            'page' => 1,
            'per_page' => 0,
            'total' => 0
          ]
        ]);
      }

      // Lấy thông tin phòng từ các hợp đồng
      $contractIds = [];
      foreach ($contractTenants as $contractTenant) {
        $contractIds[] = $contractTenant->contract_id;
      }

      $contracts = HostContractModel::find([
        'conditions' => 'id IN ({contractIds:array})',
        'bind' => ['contractIds' => $contractIds],
        'order' => 'created DESC'
      ]);

      // Phân trang
      $page = $this->request->getQuery('page', 'int', 1);
      $perPage = $this->request->getQuery('per_page', 'int', 10);

      $data = $this->getPagination($contracts, $perPage, $page);
      $items = [];

      foreach ($data as $contract) {
        $room = $contract->room;
        $hostel = $contract->hostel;

        // Tìm tenant lead của hợp đồng này
        $leadTenant = null;
        foreach ($contract->tenants as $contractTenant) {
          if ($contractTenant->is_lead == 1) {
            $leadTenant = $contractTenant->tenant;
            break;
          }
        }

        $items[] = [
          'room_id' => $room->id,
          'room_title' => $room->title,
          'room_area' => $room->area,
          'room_maximum' => $room->maximum,
          'hostel_name' => $hostel->name,
          'hostel_address' => $hostel->full_address,
          'contract_id' => $contract->id,
          'contract_code' => $contract->code,
          'contract_price' => $contract->price,
          'contract_deposit' => $contract->price_deposit,
          'contract_start_date' => $contract->start_date,
          'contract_end_date' => $contract->end_date,
          'contract_expired_date' => $contract->expired_date,
          'contract_status' => $this->_getContractStatus($contract),
          'lead_tenant' => $leadTenant ? [
            'fullname' => $leadTenant->fullname,
            'phone' => $leadTenant->phone
          ] : null,
          'tenant_count' => $contract->tenants->count()
        ];
      }

      $pagination = [
        'page' => $page,
        'per_page' => $perPage > $contracts->count() ? $contracts->count() : $perPage,
        'total' => $contracts->count()
      ];

      return $this->sendSuccessResponse('Danh sách phòng thuê', [
        'items' => $items,
        'pagination' => $pagination
      ]);

    } catch (\Exception $e) {
      return $this->sendErrorResponse('Có lỗi xảy ra: ' . $e->getMessage());
    }
  }

  /**
   * API lấy chi tiết của một phòng cụ thể mà user đang thuê/đã từng thuê
   * GET /api/client/rental/rooms/{roomId}
   */
  public function getRoomDetailAction($roomId)
  {
    try {
      $authHeader = $this->request->getHeader('Authorization');
      $payload = $this->getPayload($authHeader);

      if (empty($payload) || empty($payload->userId)) {
        return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
      }

      $user = UserModel::findFirstById($payload->userId);
      if (!$user) {
        return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_REQUEST);
      }

      // Kiểm tra quyền truy cập phòng
      if (!$this->_hasRoomAccess($user, $roomId)) {
        return $this->sendErrorResponse('Bạn không có quyền truy cập phòng này', [], $this->statusCode::FORBIDDEN);
      }

      $room = HostRoomModel::findFirstById($roomId);
      if (!$room) {
        return $this->sendErrorResponse('Không tìm thấy phòng', [], $this->statusCode::NOT_FOUND);
      }

      // Lấy hợp đồng hiện tại của user cho phòng này
      $currentContract = $this->_getCurrentContractForRoom($user, $roomId);

      $hostel = $room->hostel_room->hostel;

      $roomDetail = [
        'room_id' => $room->id,
        'room_title' => $room->title,
        'room_area' => $room->area,
        'room_maximum' => $room->maximum,
        'room_price' => $room->price,
        'room_deposit' => $room->price_deposit,
        'room_payment_cycle' => $room->payment_cycle,
        'hostel' => [
          'id' => $hostel->id,
          'name' => $hostel->name,
          'address' => $hostel->full_address,
          'description' => $hostel->description
        ],
        'current_contract' => $currentContract ? [
          'id' => $currentContract->id,
          'code' => $currentContract->code,
          'price' => $currentContract->price,
          'deposit' => $currentContract->price_deposit,
          'start_date' => $currentContract->start_date,
          'end_date' => $currentContract->end_date,
          'expired_date' => $currentContract->expired_date,
          'status' => $this->_getContractStatus($currentContract)
        ] : null,
        'room_services' => $this->_getRoomServices($room),
        'room_images' => $this->_getRoomImages($room)
      ];

      return $this->sendSuccessResponse('Chi tiết phòng thuê', $roomDetail);

    } catch (\Exception $e) {
      return $this->sendErrorResponse('Có lỗi xảy ra: ' . $e->getMessage());
    }
  }

  /**
   * API lấy danh sách tất cả các hợp đồng của user
   * GET /api/client/rental/contracts
   */
  public function getContractsAction()
  {
    try {
      $authHeader = $this->request->getHeader('Authorization');
      $payload = $this->getPayload($authHeader);

      if (empty($payload) || empty($payload->userId)) {
        return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
      }

      $user = UserModel::findFirstById($payload->userId);
      if (!$user) {
        return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_REQUEST);
      }

      // Tìm tenant dựa trên user_id
      $tenants = HostTenantModel::find([
        'conditions' => 'user_id = :user_id: AND status = 1',
        'bind' => [
          'user_id' => $user->id
        ]
      ]);

      if ($tenants->count() == 0) {
        return $this->sendSuccessResponse('Danh sách hợp đồng', [
          'items' => [],
          'pagination' => [
            'page' => 1,
            'per_page' => 0,
            'total' => 0
          ]
        ]);
      }

      // Lấy tất cả contract_tenant của user
      $tenantIds = [];
      foreach ($tenants as $tenant) {
        $tenantIds[] = $tenant->id;
      }

      $contractTenants = HostContractTenantModel::find([
        'conditions' => 'tenant_id IN ({tenantIds:array})',
        'bind' => ['tenantIds' => $tenantIds]
      ]);

      if ($contractTenants->count() == 0) {
        return $this->sendSuccessResponse('Danh sách hợp đồng', [
          'items' => [],
          'pagination' => [
            'page' => 1,
            'per_page' => 0,
            'total' => 0
          ]
        ]);
      }

      // Lấy thông tin hợp đồng
      $contractIds = [];
      foreach ($contractTenants as $contractTenant) {
        $contractIds[] = $contractTenant->contract_id;
      }

      $contracts = HostContractModel::find([
        'conditions' => 'id IN ({contractIds:array})',
        'bind' => ['contractIds' => $contractIds],
        'order' => 'created DESC'
      ]);

      // Phân trang
      $page = $this->request->getQuery('page', 'int', 1);
      $perPage = $this->request->getQuery('per_page', 'int', 10);

      $data = $this->getPagination($contracts, $perPage, $page);
      $items = [];

      foreach ($data as $contract) {
        $room = $contract->room;
        $hostel = $contract->hostel;

        // Tìm tenant lead của hợp đồng này
        $leadTenant = null;
        foreach ($contract->tenants as $contractTenant) {
          if ($contractTenant->is_lead == 1) {
            $leadTenant = $contractTenant->tenant;
            break;
          }
        }

        $items[] = [
          'contract_id' => $contract->id,
          'contract_code' => $contract->code,
          'contract_price' => $contract->price,
          'contract_deposit' => $contract->price_deposit,
          'contract_start_date' => $contract->start_date,
          'contract_end_date' => $contract->end_date,
          'contract_expired_date' => $contract->expired_date,
          'contract_date' => $contract->contract_date,
          'contract_status' => $this->_getContractStatus($contract),
          'room' => [
            'id' => $room->id,
            'title' => $room->title,
            'area' => $room->area,
            'maximum' => $room->maximum
          ],
          'hostel' => [
            'id' => $hostel->id,
            'name' => $hostel->name,
            'address' => $hostel->full_address
          ],
          'lead_tenant' => $leadTenant ? [
            'fullname' => $leadTenant->fullname,
            'phone' => $leadTenant->phone
          ] : null,
          'tenant_count' => $contract->tenants->count()
        ];
      }

      $pagination = [
        'page' => $page,
        'per_page' => $perPage > $contracts->count() ? $contracts->count() : $perPage,
        'total' => $contracts->count()
      ];

      return $this->sendSuccessResponse('Danh sách hợp đồng', [
        'items' => $items,
        'pagination' => $pagination
      ]);

    } catch (\Exception $e) {
      return $this->sendErrorResponse('Có lỗi xảy ra: ' . $e->getMessage());
    }
  }

  /**
   * API lấy chi tiết của một hợp đồng cụ thể
   * GET /api/client/rental/contracts/{contractId}
   */
  public function getContractDetailAction($contractId)
  {
    try {
      $authHeader = $this->request->getHeader('Authorization');
      $payload = $this->getPayload($authHeader);

      if (empty($payload) || empty($payload->userId)) {
        return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
      }

      $user = UserModel::findFirstById($payload->userId);
      if (!$user) {
        return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_REQUEST);
      }

      // Kiểm tra quyền truy cập hợp đồng
      if (!$this->_hasContractAccess($user, $contractId)) {
        return $this->sendErrorResponse('Bạn không có quyền truy cập hợp đồng này', [], $this->statusCode::FORBIDDEN);
      }

      $contract = HostContractModel::findFirstById($contractId);
      if (!$contract) {
        return $this->sendErrorResponse('Không tìm thấy hợp đồng', [], $this->statusCode::NOT_FOUND);
      }

      $room = $contract->room;
      $hostel = $contract->hostel;

      // Lấy danh sách tenant trong hợp đồng
      $tenants = [];
      foreach ($contract->tenants as $contractTenant) {
        $tenant = $contractTenant->tenant;
        $tenants[] = [
          'id' => $tenant->id,
          'fullname' => $tenant->fullname,
          'phone' => $tenant->phone,
          'email' => $tenant->email,
          'birthday' => $tenant->birthday,
          'gender' => $tenant->gender,
          'national_id' => $tenant->national_id,
          'is_lead' => $contractTenant->is_lead == 1
        ];
      }

      // Lấy thông tin dịch vụ phòng
      $roomServices = $this->_getRoomServices($room);

      // Lấy thông tin hóa đơn (nếu có)
      $bills = [];
      foreach ($contract->bills as $bill) {
        $bills[] = [
          'id' => $bill->id,
          'code' => $bill->code,
          'amount' => $bill->amount,
          'status' => $bill->status,
          'created' => $bill->created,
          'due_date' => $bill->due_date ?? null
        ];
      }

      $contractDetail = [
        'contract_id' => $contract->id,
        'contract_code' => $contract->code,
        'contract_price' => $contract->price,
        'contract_deposit' => $contract->price_deposit,
        'contract_start_date' => $contract->start_date,
        'contract_end_date' => $contract->end_date,
        'contract_expired_date' => $contract->expired_date,
        'contract_date' => $contract->contract_date,
        'contract_status' => $this->_getContractStatus($contract),
        'contract_note' => $contract->note,
        'is_first_payment' => $contract->is_first_payment == 1,
        'is_deposit' => $contract->is_deposit == 1,
        'room' => [
          'id' => $room->id,
          'title' => $room->title,
          'area' => $room->area,
          'maximum' => $room->maximum,
          'price' => $room->price,
          'deposit' => $room->price_deposit,
          'payment_cycle' => $room->payment_cycle,
          'services' => $roomServices,
          'images' => $this->_getRoomImages($room)
        ],
        'hostel' => [
          'id' => $hostel->id,
          'name' => $hostel->name,
          'address' => $hostel->full_address,
          'description' => $hostel->description,
          'host_contact' => [
            'name' => $hostel->host->name ?? null,
            'phone' => $hostel->host->phone ?? null,
            'email' => $hostel->host->email ?? null
          ]
        ],
        'tenants' => $tenants,
        'bills' => $bills
      ];

      return $this->sendSuccessResponse('Chi tiết hợp đồng', $contractDetail);

    } catch (\Exception $e) {
      return $this->sendErrorResponse('Có lỗi xảy ra: ' . $e->getMessage());
    }
  }

  /**
   * Kiểm tra user có quyền truy cập phòng không
   */
  private function _hasRoomAccess($user, $roomId)
  {
    $tenants = HostTenantModel::find([
      'conditions' => 'user_id = :user_id: AND status = 1',
      'bind' => [
        'user_id' => $user->id
      ]
    ]);

    if ($tenants->count() == 0) {
      return false;
    }

    $tenantIds = [];
    foreach ($tenants as $tenant) {
      $tenantIds[] = $tenant->id;
    }

    $contractTenants = HostContractTenantModel::find([
      'conditions' => 'tenant_id IN ({tenantIds:array})',
      'bind' => ['tenantIds' => $tenantIds]
    ]);

    foreach ($contractTenants as $contractTenant) {
      $contract = $contractTenant->contract;
      if ($contract && $contract->host_room_id == $roomId) {
        return true;
      }
    }

    return false;
  }

  /**
   * Kiểm tra user có quyền truy cập hợp đồng không
   */
  private function _hasContractAccess($user, $contractId)
  {
    $tenants = HostTenantModel::find([
      'conditions' => 'user_id = :user_id: AND status = 1',
      'bind' => [
        'user_id' => $user->id
      ]
    ]);

    if ($tenants->count() == 0) {
      return false;
    }

    $tenantIds = [];
    foreach ($tenants as $tenant) {
      $tenantIds[] = $tenant->id;
    }

    $contractTenant = HostContractTenantModel::findFirst([
      'conditions' => 'tenant_id IN ({tenantIds:array}) AND contract_id = :contractId:',
      'bind' => [
        'tenantIds' => $tenantIds,
        'contractId' => $contractId
      ]
    ]);

    return $contractTenant !== false;
  }

  /**
   * Lấy hợp đồng hiện tại của user cho phòng cụ thể
   */
  private function _getCurrentContractForRoom($user, $roomId)
  {
    $tenants = HostTenantModel::find([
      'conditions' => 'user_id = :user_id: AND status = 1',
      'bind' => [
        'user_id' => $user->id
      ]
    ]);

    if ($tenants->count() == 0) {
      return null;
    }

    $tenantIds = [];
    foreach ($tenants as $tenant) {
      $tenantIds[] = $tenant->id;
    }

    $contractTenants = HostContractTenantModel::find([
      'conditions' => 'tenant_id IN ({tenantIds:array})',
      'bind' => ['tenantIds' => $tenantIds]
    ]);

    foreach ($contractTenants as $contractTenant) {
      $contract = $contractTenant->contract;
      if ($contract && $contract->host_room_id == $roomId) {
        // Trả về hợp đồng mới nhất
        return $contract;
      }
    }

    return null;
  }

  /**
   * Lấy trạng thái hợp đồng
   */
  private function _getContractStatus($contract)
  {
    $now = date('Y-m-d');

    if ($contract->end_date && $contract->end_date <= $now) {
      return [
        'code' => 'ended',
        'label' => 'Đã kết thúc'
      ];
    }

    if ($contract->expired_date && $contract->expired_date <= $now) {
      return [
        'code' => 'expired',
        'label' => 'Đã hết hạn'
      ];
    }

    if ($contract->start_date && $contract->start_date <= $now) {
      return [
        'code' => 'active',
        'label' => 'Đang hiệu lực'
      ];
    }

    return [
      'code' => 'pending',
      'label' => 'Chờ hiệu lực'
    ];
  }

  /**
   * Lấy thông tin dịch vụ phòng
   */
  private function _getRoomServices($room)
  {
    $services = [];

    if ($room->services) {
      foreach ($room->services as $service) {
        $services[] = [
          'id' => $service->id,
          'name' => $service->name,
          'price' => $service->price,
          'unit' => $service->unit
        ];
      }
    }

    return $services;
  }

  /**
   * Lấy hình ảnh phòng
   */
  private function _getRoomImages($room)
  {
    $images = [];

    if ($room->hostel_room && $room->hostel_room->imgs) {
      $imgs = json_decode($room->hostel_room->imgs, true);
      if (is_array($imgs)) {
        foreach ($imgs as $img) {
          $images[] = [
            'url' => $img,
            'alt' => $room->title
          ];
        }
      }
    }

    // Nếu không có hình ảnh từ hostel_room, lấy hình ảnh chính
    if (empty($images) && $room->hostel_room && $room->hostel_room->image) {
      $images[] = [
        'url' => $room->hostel_room->image,
        'alt' => $room->title
      ];
    }

    return $images;
  }
}