<?php

namespace Modules\Frontend\Controllers\Api\Client;

use Modules\App\Models\AdHostelModel;
use Modules\App\Models\AdHostelSavedModel;
use Modules\App\Models\LocationAreaModel;
use Modules\App\Models\UserModel;
use Modules\Frontend\Controllers\Api\ApiControllerBase;

class AdHostelApiController extends ApiControllerBase
{
  public function listAction()
  {
    try {
      $authHeader = $this->request->getHeader('Authorization');
      $payload = $this->getPayload($authHeader);

      if (empty($payload) || empty($payload->userId)) {
        $user = null;
      } else {
        $user = UserModel::findFirstById($payload->userId);
      }

      $params           = [];
      $bindParams       = [];
      $whereConditions  = ["AH.is_published = 1", "AH.locked = 0"];

      // Lọc theo gi<PERSON>
      if ($this->request->getQuery('price_min')) {
        $whereConditions[]        = "AH.price >= :price_min:";
        $bindParams['price_min']  = $this->request->getQuery('price_min', 'int');
      }

      if ($this->request->getQuery('price_max')) {
        $whereConditions[] = "AH.price <= :price_max:";
        $bindParams['price_max'] = $this->request->getQuery('price_max', 'int');
      }

        // if ($this->request->getQuery('area_min')) {
        //   $whereConditions[] = "AH.area >= :area_min:";
        //   $bindParams['area_min'] = $this->request->getQuery('area_min', 'int');
        // }
        
        // if($this->request->getQuery('area_max')) {
        //   $whereConditions[] = "AH.area <= :area_max:";
        //   $bindParams['area_max'] = $this->request->getQuery('area_max', 'int');
        // }

      if ($this->request->getQuery('type')) {
        $whereConditions[] = "H.type_id = :type:";
        $bindParams['type'] = $this->request->getQuery('type', 'string');
      }

      // Lọc theo khu vực
      if ($this->request->getQuery('district')) {
        $whereConditions[] = "H.district_code = :district:";
        $bindParams['district'] = $this->request->getQuery('district', 'string');
      }

      if ($this->request->getQuery('street')) {
        $whereConditions[] = "H.street_code = :street:";
        $bindParams['street'] = $this->request->getQuery('street', 'string');
      }

      if ($this->request->getQuery('location')) {
        $whereConditions[] = "AHLD.location_id = :location:";
        $bindParams['location'] = $this->request->getQuery('location', 'int');
      }

      if ($this->request->getQuery('province')) {
        $whereConditions[] = "H.province_code = :province:";
        $bindParams['province'] = $this->request->getQuery('province', 'string');
      } 

      // Lọc theo tiện nghi
      if ($this->request->getQuery('amenities')) {
        $amenities = explode(',', $this->request->getQuery('amenities'));
        $amenitiesConditions = [];
        foreach ($amenities as $index => $amenity) {
          $paramName = "amenity_$index";
          $amenitiesConditions[] = "AH.amenities LIKE :%$paramName%:";
          $bindParams[$paramName] = $amenity;
        }
        if (!empty($amenitiesConditions)) {
          $whereConditions[] = "(" . implode(" OR ", $amenitiesConditions) . ")";
        }
      }

      // Lọc theo đối tượng
      if ($this->request->getQuery('object_type')) {
        $objectTypes      = explode(',', $this->request->getQuery('object_type'));
        $objectConditions = [];
        foreach ($objectTypes as $index => $objectType) {
          $paramName = "object_$index";
          $objectConditions[] = "AH.object_type LIKE :%$paramName%:";
          $bindParams[$paramName] = $objectType;
        }
        if (!empty($objectConditions)) {
          $whereConditions[] = "(" . implode(" OR ", $objectConditions) . ")";
        }
      }

      // Lọc theo môi trường
      if ($this->request->getQuery('environment')) {
        $environments = explode(',', $this->request->getQuery('environment'));
        $envConditions = [];
        foreach ($environments as $index => $env) {
          $paramName = "env_$index";
          $envConditions[] = "AH.environment LIKE :%$paramName%:";
          $bindParams[$paramName] = $env;
        }
        if (!empty($envConditions)) {
          $whereConditions[] = "(" . implode(" OR ", $envConditions) . ")";
        }
      }

      // Sắp xếp
      $sort = $this->request->getQuery('sort', 'string', 'newest');
      switch ($sort) {
        case 'price_asc':
          $params['order'] = "AH.price ASC";
          break;
        case 'price_desc':
          $params['order'] = "AH.price DESC";
          break;
        case 'newest':
        default:
          $params['order'] = "AH.published DESC";
          break;
      }

      // Phân trang
      $page             = $this->request->getQuery('page', 'int', 1);
      $perPage          = $this->request->getQuery('per_page', 'int', 20);
      $limit            = $this->request->getQuery('limit', 'int', 20);

      $params['limit']  = $limit;

      // Tạo điều kiện WHERE
      if (!empty($whereConditions)) {
        $params['where'] = implode(" AND ", $whereConditions);
      }

      $params['bind'] = $bindParams;

      // Lấy dữ liệu
      $adHostelModel  = new AdHostelModel();
      $results        = $adHostelModel->GetItem($params);

      $data = $this->getPagination($results, $perPage, $page);
      $item = [];
      foreach ($data as $value) {
        $item[] = $value->DTOClientListItem($user->id);
      }

      $pagination = [
        'page'            => (int)$page,
        'per_page'        => $results->count() > $perPage ? (int)$perPage : (int)$results->count(),
        'total_page'      => (int)$data->getLastPage(),
        'total_item'      => (int)$results->count() ?? 0,
      ];

      return $this->sendSuccessResponse('Tìm kiếm thành công', [
        'items'       => $item,
        'pagination'  => $pagination
      ]);

    } catch (\Exception $e) {
      return $this->sendErrorResponse('Có lỗi xảy ra: ' . $e->getMessage());
    }
  }

  /**
   * Lấy chi tiết tin đăng trọ
   */
  public function detailAction($id)
  {
    try {

      $authHeader = $this->request->getHeader('Authorization');
      $payload = $this->getPayload($authHeader);

      if (empty($payload) || empty($payload->userId)) {
        $user = null;
      } else {
        $user = UserModel::findFirstById($payload->userId);
      }

      $adHostel = AdHostelModel::findFirstById($id);

      if (!$adHostel) {
        return $this->sendErrorResponse('Không tìm thấy tin đăng', [], $this->statusCode::NOT_FOUND);
      }

      if (!$adHostel->is_published || $adHostel->locked) {
        return $this->sendErrorResponse('Tin đăng không khả dụng', [], $this->statusCode::BAD_REQUEST);
      }

      $adHostel->hitUp();
      return $this->sendSuccessResponse('Chi tiết tin', $adHostel->DTOClientDetail($user ? $user->id : null));
    } catch (\Exception $e) {
      return $this->sendErrorResponse($e->getMessage());
    }
  }

  /**
   * Lưu tin trọ yêu thích
   */
  public function saveHostelAction($id)
  {
    try {
      $authHeader = $this->request->getHeader('Authorization');
      $payload = $this->getPayload($authHeader);

      if (empty($payload) || empty($payload->userId)) {
        return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
      }

      $user = UserModel::findFirst([
        'conditions' => 'id = :id:',
        'bind' => ['id' => $payload->userId]
      ]);

      if (!$user) {
        return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_REQUEST);
      }

      $adHostel = AdHostelModel::findFirstById($id);

      if (!$adHostel) {
        return $this->sendErrorResponse('Không tìm thấy tin', [], $this->statusCode::NOT_FOUND);
      }

      $saved = AdHostelSavedModel::findFirst([
        'conditions' => 'ad_hostel_id = :ad_hostel_id: AND user_id = :user_id:',
        'bind' => [
          'ad_hostel_id' => $id,
          'user_id' => $user->id
        ]
      ]);

      if ($saved) {
        $saved->delete();
        return $this->sendSuccessResponse('Đã bỏ lưu tin thành công', ['is_saved' => false]);
      } else {
        $saved = new AdHostelSavedModel();
        $saved->ad_hostel_id  = $id;
        $saved->user_id       = $user->id;
        $saved->created       = date('Y-m-d H:i:s');
        $saved->save();
        return $this->sendSuccessResponse('Đã lưu tin thành công', ['is_saved' => true]);
      }
    } catch (\Exception $e) {
      return $this->sendErrorResponse('Có lỗi xảy ra: ' . $e->getMessage());
    }
  }

  public function getSavedAction()
  {
    try {
      $authHeader = $this->request->getHeader('Authorization');
      $payload = $this->getPayload($authHeader);

      if (empty($payload) || empty($payload->userId)) {
        return $this->sendErrorResponse('Token hết hạn hoặc không hợp lệ', [], $this->statusCode::BAD_UNAUTHORIZED);
      }

      $page = $this->request->getQuery('page', 'int', 1);
      $perPage = $this->request->getQuery('per_page', 'int', 10);

      $user = UserModel::findFirstById($payload->userId);

      if (!$user) {
        return $this->sendErrorResponse('Người dùng không tồn tại', [], $this->statusCode::BAD_REQUEST);
      }

      $saved = AdHostelSavedModel::find([
        'conditions' => 'user_id = :user_id:',
        'bind' => ['user_id' => $user->id]
      ]);

      $data = $this->getPagination($saved, $perPage, $page);
      $item = [];
      foreach ($data as $value) {
        $item[] = $value->adHostel->DTOClientListItem($user->id);
      }

      $pagination = [
        'page' => $page,
        'per_page' => $perPage > $saved->count() ? $saved->count() : $perPage,
        'total' => $saved->count()
      ];

      $result = [
        'items' => $item,
        'pagination' => $pagination
      ];

      return $this->sendSuccessResponse('Danh sách trọ đã lưu', $result);
    } catch (\Exception $e) {
      return $this->sendErrorResponse('Có lỗi xảy ra: ' . $e->getMessage());
    }
  }
}
