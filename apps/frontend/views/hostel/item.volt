{% set premium = Ohi.getPremiumStatus(item.id) %}
{% set hostelFavorite = element.CheckAdHostelSaved(item.id) ? 'active' : '' %}
{% set sale = item.price_sale is not empty and item.price_sale > 0 ? item.price_sale : '' %}
{% set thumb = item.thumb() is not empty ? item.thumb() : '/frontend/home/<USER>/no-image.svg' %}

<div class="hostel-item {{ style is defined and style is not empty ? style : null }}">
  <div class="hostel-item__img">
    <a href="{{ link }}" class="hostel-item__img--link">
      <img decoding="async" loading="lazy" src="{{ url(thumb) }}" alt="{{ item.hostel.name }}">
    </a>
    {% if sale is defined and sale is not empty %}
      <div class="tag for-sale hint--top hint--rounded" aria-label="Sale">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Capa_1" x="0px" y="0px" viewBox="0 0 511.548 511.548" style="enable-background:new 0 0 511.548 511.548;" xml:space="preserve" width="100%" height="100%"><g>	<path style="fill:#FF6200;" d="M394.441,191.548C307.52,95.547,287.775,20.882,287.775,20.882s-15.054,6.718-32,22.11   l-21.333,244.556l21.333,209h0.001c104.842-0.001,189.833-84.992,189.833-189.833C445.608,263.409,421.423,221.349,394.441,191.548   z"/>	<path style="fill:#FD7D21;" d="M223.775,84.882c-10.873,21.747-13.434,46.265-13.33,65.08c0.1,18.252-12.758,34.004-30.655,37.584   c-12.504,2.501-25.43-1.413-34.447-10.43l-17.568-17.568c0,0-26.044,35.911-30.507,42.667   c-20.047,30.346-31.613,66.786-31.321,105.945c0.778,104.581,85.244,188.388,189.828,188.388V42.992   C244.69,53.06,232.797,66.838,223.775,84.882z"/>	<g>		<path style="fill:#FFB62D;" d="M405.561,181.48c-43.372-47.903-69.147-90.072-83.134-117.013    c-15.148-29.181-20.112-47.276-20.15-47.42L297.768,0l-16.104,7.183c-0.917,0.409-11.941,5.434-25.89,16.238l-10.667,18.794    l10.667,22.117c8.336-9.351,16.934-16.341,23.849-21.18c11.282,28.696,39.881,87.981,103.699,158.465    c14.217,15.702,47.285,57.376,47.285,105.099c0,96.403-78.43,174.833-174.832,174.833h-0.001l-10.667,19.333l10.667,10.667h0.001    c112.945-0.001,204.832-91.888,204.832-204.833C460.608,265.764,440.544,220.118,405.561,181.48z"/>		<path style="fill:#FDCB02;" d="M132.499,430.925c-32.898-32.646-51.206-76.285-51.553-122.876    c-0.26-34.878,9.712-68.616,28.837-97.565c2.335-3.534,11.702-16.602,19.833-27.879l5.119,5.119    c12.592,12.592,30.53,18.025,47.996,14.532c24.888-4.978,42.852-27.004,42.713-52.375c-0.087-15.701,1.881-38.558,11.746-58.29    c5.351-10.702,11.883-19.741,18.584-27.258V23.421c-14.692,11.381-32.628,29.175-45.417,54.753    c-12.515,25.031-15.018,52.9-14.913,71.87c0.061,11.04-7.761,20.626-18.598,22.793c-7.598,1.518-15.414-0.844-20.898-6.328    l-29.997-29.997l-10.319,14.229c-1.071,1.477-26.289,36.256-30.88,43.205c-22.419,33.937-34.109,73.47-33.806,114.325    c0.406,54.565,21.864,105.686,60.421,143.948c38.554,38.259,89.839,59.329,144.407,59.329v-30    C209.176,481.548,165.396,463.57,132.499,430.925z"/>	</g>	<g>		<path style="fill:#ED3800;" d="M255.775,206.042c-0.111,0-0.222,0.004-0.333,0.004l-24.997,117.329l24.997,117.329    c0.111,0,0.222,0.004,0.333,0.004c64.801,0,117.333-52.532,117.333-117.333C373.108,258.574,320.576,206.042,255.775,206.042z"/>		<path style="fill:#FF4B00;" d="M138.441,323.375c0,64.69,52.352,117.149,117,117.329V206.046    C190.794,206.226,138.441,258.685,138.441,323.375z"/>	</g>	<g>		<polygon style="fill:#D9E7EC;" points="319.432,254.503 286.177,254.503 255.441,299.513 245.108,340.882 255.441,348.214   "/>		<path style="fill:#D9E7EC;" d="M306.248,317.472c-20.858,0-36.601,13.971-36.601,38.372c0,24.597,15.742,38.371,36.601,38.371    s36.601-13.774,36.601-38.371C342.849,331.443,327.106,317.472,306.248,317.472z M306.248,372.963    c-4.329,0-8.658-3.936-8.658-17.12c0-13.184,4.329-17.12,8.658-17.12s8.658,3.936,8.658,17.12    C314.906,369.027,310.577,372.963,306.248,372.963z"/>		<polygon style="fill:#FAFCFD;" points="225.372,392.247 255.441,348.214 255.441,299.513 192.117,392.247   "/>		<path style="fill:#FAFCFD;" d="M241.902,290.907c0-24.4-15.742-38.372-36.601-38.372s-36.601,13.971-36.601,38.372    c0,24.597,15.742,38.372,36.601,38.372S241.902,315.504,241.902,290.907z M196.643,290.907c0-13.184,4.329-17.12,8.658-17.12    c4.329,0,8.658,3.936,8.658,17.12c0,13.184-4.329,17.12-8.658,17.12C200.972,308.027,196.643,304.091,196.643,290.907z"/>	</g></g></svg>
      </div>
    {% endif %}
    <div class="hostel-item__img--save heart-{{item.id}} {{ hostelFavorite }} stopPropagation" onclick="hostelSave({{ item.id }}, {{ user.item is defined ? user.item.id : 0 }})">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" aria-hidden="true" role="presentation" focusable="false" style="display: block; stroke-width: 2; overflow: visible;"><path d="M16 28c7-4.73 14-10 14-17a6.98 6.98 0 0 0-7-7c-1.8 0-3.58.68-4.95 2.05L16 8.1l-2.05-2.05a6.98 6.98 0 0 0-9.9 0A6.98 6.98 0 0 0 2 11c0 7 7 12.27 14 17z"></path></svg>
    </div>
    {% if premium is not empty %}
      {% if premium.premium_type == 'vip' and date('Y-m-d H:i:s', strtotime(premium.expired)) >= date("Y-m-d H:i:s") %}
        <div class="hostel-item__img--tag vip"><p>vip</p></div>
      {% elseif premium.premium_type == 'hot' and date('Y-m-d H:i:s', strtotime(premium.expired)) >= date("Y-m-d H:i:s") %}
        <div class="hostel-item__img--tag hot"><p>hot</p></div>
      {% endif %}
    {% endif %}
    {% if item.url_review is not empty %}
      <div class="hostel-item__img--action">
        <div class="btn-review" rel="{{ item.url_review }}" onclick="getVideoReview(event, this)">
          <div class="btn-review__wrap">
            <div class="icon">
              <i class="fa-solid fa-play"></i>
            </div>
            <p>Review</p>
          </div>
        </div>
      </div>
    {% endif %}
  </div>
  <div class="hostel-item__body">
    <div class="hostel-item__title">
      <a href="{{ link }}" class="hostel-item__link">
        <h3>{{ item.hostel.name }}</h3>
        {{ item.sticked == 1 ? '<span class="hint--left hint--rounded" aria-label="Xác thực"><i class="fa-solid fa-circle-check"></i></span>' : ''}}
      </a>
    </div>
    <div class="hostel-item__price--wrap">
      
      <div class="hostel-item__price">
        
        <small>Từ</small>
        <div class="hostel-item__price {{ sale is defined and sale is not empty ? 'hostel-item__price--old' : '' }}">
          {% if item.price >= 100000 %}
              <span>{{ Ohi.formatPrice(item.price) }}/tháng</span>
            {% else %}
            <span>Liên hệ lấy giá</span>
          {% endif %}
        </div>
        {% if sale is defined and sale is not empty %}
          <span>{{ Ohi.formatPrice(sale) }}/tháng</span>
        {% endif %}
      </div>
    </div>
    <div class="hostel-item__list">
      <a href="{{ url('/' ~ item.hostel.hostel_type.code) }}" class="item">{{ item.hostel.hostel_type.title }}</a>
      {% if item.hostel.area is not empty %}
        <div class="item">{{ item.hostel.area }}m²</div>
      {% endif %}
    </div>
    <div class="hostel-item__address">
      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 14 14" fill="none">
        <path d="M7.28205 0C3.81795 0 1 2.81795 1 6.28205C1 9.95651 4.3722 12.1836 6.60358 13.6575L6.98338 13.9096C7.07385 13.9699 7.17795 14 7.28205 14C7.38615 14 7.49026 13.9699 7.58072 13.9096L7.96052 13.6575C10.1919 12.1836 13.5641 9.95651 13.5641 6.28205C13.5641 2.81795 10.7462 0 7.28205 0ZM7.36749 12.7587L7.28205 12.8155L7.19661 12.7587C5.03559 11.3314 2.07692 9.37713 2.07692 6.28205C2.07692 3.41169 4.41169 1.07692 7.28205 1.07692C10.1524 1.07692 12.4872 3.41169 12.4872 6.28205C12.4872 9.37713 9.5278 11.3321 7.36749 12.7587ZM7.28205 3.94872C5.99549 3.94872 4.94872 4.99549 4.94872 6.28205C4.94872 7.56862 5.99549 8.61539 7.28205 8.61539C8.56862 8.61539 9.61539 7.56862 9.61539 6.28205C9.61539 4.99549 8.56862 3.94872 7.28205 3.94872ZM7.28205 7.53846C6.58923 7.53846 6.02564 6.97487 6.02564 6.28205C6.02564 5.58923 6.58923 5.02564 7.28205 5.02564C7.97487 5.02564 8.53846 5.58923 8.53846 6.28205C8.53846 6.97487 7.97487 7.53846 7.28205 7.53846Z" fill="currentColor"/>
      </svg>
      {% if item.hostel.district is defined or item.hostel.province is defined %}
        <p>{{ item.hostel.district is defined ? item.hostel.district.name : '' }}{{ item.hostel.district is defined and item.hostel.province is defined ? ', ' : '' }}{{ item.hostel.province is defined ? item.hostel.province.type ~ ' ' ~ item.hostel.province.name : '' }}
        </p>
      {% endif %}
    </div>
  </div>
</div>

<script type="application/ld+json">
  {
    "@type":"Hostel",
    "@context":"http://schema.org",
    "name": "{{ Ohi.formatStringSchema(item.hostel.name) }}",
    "description": "{{ Ohi.formatStringSchema(item.content|striptags|slice(0, 169)~(item.content|length > 169 ? '...' : '')) }}",
    "address":{
      "@type":"PostalAddress",
      "@context":"http://schema.org",
      "streetAddress":"{{ item.hostel.address }}",
      "addressLocality":"{{ item.hostel.province.name }}",
      "addressCountry":{
        "@type":"Country",
        "name":"VN"
      }
    },
    "url":"{{ url('/' ~ item.hostel.hostel_type.code ~ '/' ~ item.hostel.code) }}",
    "image": "{{ url(item.hostel.image) }}",
    "priceRange": "{{ item.price }}",
    "telephone": "{{ item.phone is not empty ? item.phone : (item.hostel.host is defined and item.hostel.host.phone is not empty ? item.hostel.host.phone : Ohi.sysConfig('hotline')) }}"
  }
</script>