<?php

namespace Modules\Library\Casso;

class Casso
{
  private $memoPrefix;
  private $acceptableDifference;
  private $checksumKey;

  public function __construct($config = null)
  {
    $this->memoPrefix = $config['memo_prefix'] ?? 'TM';
    $this->acceptableDifference = $config['acceptable_difference'] ?? 1000;
    $this->checksumKey = $config['checksum_key'] ?? '';
  }

  /**
   * Parse the order ID from transaction description
   *
   * @param string $description Transaction description
   * @return string|null Order ID or null if not found
   */
  public function parseOrderId($description)
  {
    if (empty($description)) {
      return null;
    }
    
    $escapedPrefix = preg_quote($this->memoPrefix, '/');
    
    $pattern = '/(?:^|\s|\.|\_|\-|[^A-Z0-9])' . $escapedPrefix . '([A-Z0-9]+)(?=\s|\.|\_|\-|$|[^A-Z0-9])/mi';
    
    if (preg_match($pattern, $description, $match)) {
      if (isset($match[1]) && !empty(trim($match[1]))) {
        return trim($match[1]);
      }
    }
    
    // Ghi log cho các trường hợp không thể xác định mã đơn hàng
    error_log('Debug: Unable to parse order_id from description: ' . $description);
    
    return null;
  }

  /**
   * Process webhook data
   *
   * @param object $jsonBody JSON body from webhook
   * @return array Transactions array
   * @throws \Exception If webhook data contains error or has invalid format
   */
  public function processWebhookData($jsonBody)
  {
    if ($jsonBody->error != 0) {
      throw new \Exception("Error in Casso webhook data: {$jsonBody->error}");
    }

    if (is_object($jsonBody->data)) {
      return [$jsonBody->data];
    }

    throw new \Exception("Invalid webhook data format");
  }

  /**
   * Check if payment amount is within acceptable range
   *
   * @param float $paid Amount paid
   * @param float $expected Expected amount
   * @return string Payment status: 'underpaid', 'paid', or 'overpaid'
   */
  public function checkPaymentStatus($paid, $expected)
  {
    $acceptableDifference = abs($this->acceptableDifference);

    if ($paid < $expected - $acceptableDifference) {
      return 'underpaid';
    } 
    
    return '00';
  }

  /**
   * Get payment status name in Vietnamese
   * 
   * @param string $status Payment status code
   * @return string Payment status in Vietnamese
   */
  public function getNamePaymentStatus($status) 
  {
    $statusNames = [
      'underpaid' => 'Thanh toán thiếu',
      '00' => 'Đã thanh toán',
      'overpaid' => 'Thanh toán dư'
    ];
    
    return $statusNames[$status] ?? '';
  }

  /**
   * Get normalized headers from request
   *
   * @return array Headers
   */
  public function getHeaders()
  {
    $headers = [];
    $copyServer = [
      'CONTENT_TYPE' => 'Content-Type',
      'CONTENT_LENGTH' => 'Content-Length',
      'CONTENT_MD5' => 'Content-Md5',
    ];

    foreach ($_SERVER as $key => $value) {
      if (substr($key, 0, 5) === 'HTTP_') {
        $key = substr($key, 5);
        if (!isset($copyServer[$key]) || !isset($_SERVER[$key])) {
          $key = str_replace(' ', '-', ucwords(strtolower(str_replace('_', ' ', $key))));
          $headers[$key] = $value;
        }
      } elseif (isset($copyServer[$key])) {
        $headers[$copyServer[$key]] = $value;
      }
    }

    $this->setAuthorizationHeader($headers);

    return $headers;
  }

  /**
   * Set authorization header if not present
   * 
   * @param array &$headers Headers array to modify
   */
  private function setAuthorizationHeader(&$headers)
  {
    if (isset($headers['Authorization'])) {
      return;
    }
    
    if (isset($_SERVER['REDIRECT_HTTP_AUTHORIZATION'])) {
      $headers['Authorization'] = $_SERVER['REDIRECT_HTTP_AUTHORIZATION'];
    } elseif (isset($_SERVER['PHP_AUTH_USER'])) {
      $basic_pass = $_SERVER['PHP_AUTH_PW'] ?? '';
      $headers['Authorization'] = 'Basic ' . base64_encode($_SERVER['PHP_AUTH_USER'] . ':' . $basic_pass);
    } elseif (isset($_SERVER['PHP_AUTH_DIGEST'])) {
      $headers['Authorization'] = $_SERVER['PHP_AUTH_DIGEST'];
    }
  }

  /**
   * Verify webhook signature using HMAC SHA-512
   *
   * @param array $headers Request headers
   * @param array $data Request data
   * @return bool
   */
  public function verifyWebhookSignature($headers, $data)
  {
    if (empty($this->checksumKey)) {
      error_log('Debug: No checksum key provided');
      return true;
    }

    if (empty($headers['X-Casso-Signature'])) {
      error_log('Debug: No X-Casso-Signature header found');
      return false;
    }

    $receivedSignature = $headers['X-Casso-Signature'];
    error_log('Debug: Received signature: ' . $receivedSignature);

    if (!preg_match('/t=(\d+),v1=([a-f0-9]+)/', $receivedSignature, $matches) || count($matches) < 3) {
      error_log('Debug: Failed to parse signature format');
      return false;
    }

    $timestamp = (int)$matches[1];
    $signature = $matches[2];
    
    $sortedDataByKey = $this->sortObjDataByKey($data);
    $jsonData = json_encode($sortedDataByKey);
    
    $messageToSign = $timestamp . '.' . $jsonData;
    $generatedSignature = hash_hmac('sha512', $messageToSign, $this->checksumKey);
    
    $result = $signature === $generatedSignature;
    error_log('Debug: Signature match: ' . ($result ? 'Yes' : 'No'));

    return $result;
  }

  /**
   * Test parse order ID from a sample description
   * 
   * @param string $description Test description
   * @return array Result with full match and extracted ID
   */
  public function testParseOrderId($description)
  {
    if (empty($description)) {
      return ['success' => false, 'message' => 'Empty description'];
    }
    
    $escapedPrefix = preg_quote($this->memoPrefix, '/');
    $pattern = '/(?:^|\s|\.|\_|\-|[^A-Z0-9])' . $escapedPrefix . '([A-Z0-9]+)(?=\s|\.|\_|\-|$|[^A-Z0-9])/mi';
    
    $result = ['success' => false, 'message' => 'No match found'];
    
    if (preg_match($pattern, $description, $matches)) {
      $result = [
        'success' => true,
        'full_match' => $matches[0] ?? '',
        'order_id' => isset($matches[1]) ? trim($matches[1]) : null,
        'pattern' => $pattern
      ];
    }
    
    return $result;
  }

  /**
   * Sort object data by key recursively
   *
   * @param mixed $data Data to sort (array or object)
   * @return array Sorted data
   */
  public function sortObjDataByKey($data)
  {
    if (!is_array($data) && !is_object($data)) {
      return $data;
    }
    
    $data = is_object($data) ? (array)$data : $data;
    $sortedObj = [];
    
    $keys = array_keys($data);
    sort($keys);

    foreach ($keys as $key) {
      $value = $data[$key];
      $sortedObj[$key] = is_array($value) || is_object($value) 
        ? $this->sortObjDataByKey($value) 
        : $value;
    }

    return $sortedObj;
  }
}
